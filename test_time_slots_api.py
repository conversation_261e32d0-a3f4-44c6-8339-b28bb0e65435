#!/usr/bin/env python3
"""
Test script to verify the time slots API endpoint is working
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8080/api/v1"
FACILITY_ID = "1"  # Using facility ID 1 (CHUK)
HEALTH_WORKER_ID = "2"  # Using health worker ID 2 (<PERSON>. <PERSON>)
DATE = "2025-07-21"  # Today's date

def test_time_slots_endpoint():
    """Test the /appointments/available-slots endpoint"""
    
    print("🧪 Testing Time Slots API Endpoint")
    print("=" * 50)
    
    # Test URL
    url = f"{BASE_URL}/appointments/available-slots"
    
    # Test parameters
    params = {
        'facilityId': FACILITY_ID,
        'date': DATE,
        'healthWorkerId': HEALTH_WORKER_ID
    }
    
    print(f"📍 URL: {url}")
    print(f"📋 Parameters: {params}")
    print()
    
    try:
        # Make the request
        print("🚀 Making API request...")
        response = requests.get(url, params=params, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            print("✅ SUCCESS! API endpoint is working")
            data = response.json()
            print(f"📦 Response Data:")
            print(json.dumps(data, indent=2))
            
            # Check if time slots are present
            if 'timeSlots' in data and data['timeSlots']:
                print(f"\n🎯 Found {len(data['timeSlots'])} time slots")
                print("📅 First few time slots:")
                for i, slot in enumerate(data['timeSlots'][:3]):
                    print(f"  {i+1}. {slot.get('startTime', 'N/A')} - {slot.get('endTime', 'N/A')}")
            else:
                print("\n⚠️  No time slots found in response")
                
        else:
            print(f"❌ FAILED! Status code: {response.status_code}")
            print(f"📄 Response Text: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Cannot connect to backend server")
        print("💡 Make sure the backend is running on http://localhost:8080")
        
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT ERROR: Request took too long")
        
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")

def test_without_health_worker():
    """Test the endpoint without health worker ID"""
    
    print("\n" + "=" * 50)
    print("🧪 Testing WITHOUT Health Worker ID")
    print("=" * 50)
    
    url = f"{BASE_URL}/appointments/available-slots"
    params = {
        'facilityId': FACILITY_ID,
        'date': DATE
    }
    
    print(f"📍 URL: {url}")
    print(f"📋 Parameters: {params}")
    print()
    
    try:
        response = requests.get(url, params=params, timeout=10)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS! Endpoint works without health worker ID")
            data = response.json()
            if 'timeSlots' in data:
                print(f"🎯 Found {len(data['timeSlots'])} time slots")
        else:
            print(f"❌ FAILED! Status code: {response.status_code}")
            print(f"📄 Response Text: {response.text}")
            
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    test_time_slots_endpoint()
    test_without_health_worker()
    
    print("\n" + "=" * 50)
    print("🏁 Test Complete")
    print("=" * 50)

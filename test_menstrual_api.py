#!/usr/bin/env python3
import requests
import json

# API Testing Script for Menstrual Cycle APIs
BASE_URL = "http://localhost:8080/api/v1"

def test_login():
    """Test login and get token"""
    print("🔵 Testing Login API...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "client123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        response.raise_for_status()
        
        data = response.json()
        if data.get('success'):
            token = data['data']['accessToken']
            user_id = data['data']['user']['id']
            print(f"✅ Login successful - User ID: {user_id}")
            print(f"   Token: {token[:20]}...")
            return token, user_id
        else:
            print(f"❌ Login failed: {data.get('message')}")
            return None, None
            
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None, None

def test_menstrual_cycles(token):
    """Test menstrual cycles API"""
    print("\n🩸 Testing Menstrual Cycles API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/menstrual-cycles?page=0&size=20", headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('success'):
            cycles = data.get('data', [])
            print(f"✅ Menstrual cycles retrieved - Total: {len(cycles)}")
            if cycles:
                print(f"   Sample cycle: Start Date: {cycles[0].get('startDate')}")
            else:
                print("   No cycles found - this is normal for new users")
            return True
        else:
            print(f"❌ Menstrual cycles failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Menstrual cycles error: {e}")
        return False

def test_create_menstrual_cycle(token):
    """Test creating a menstrual cycle"""
    print("\n➕ Testing Create Menstrual Cycle API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    cycle_data = {
        "startDate": "2025-01-01",
        "cycleLength": 28,
        "flowDuration": 5,
        "notes": "Test cycle from API"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/menstrual-cycles", json=cycle_data, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('success'):
            cycle = data.get('data')
            print(f"✅ Menstrual cycle created successfully")
            print(f"   Cycle ID: {cycle.get('id')}, Start Date: {cycle.get('startDate')}")
            return cycle.get('id')
        else:
            print(f"❌ Create cycle failed: {data.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ Create cycle error: {e}")
        return None

def test_medications(token):
    """Test medications API"""
    print("\n💊 Testing Medications API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/medications?page=0&size=20", headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('success'):
            medications = data.get('data', [])
            print(f"✅ Medications retrieved - Total: {len(medications)}")
            if medications:
                print(f"   Sample medication: {medications[0].get('name')}")
            else:
                print("   No medications found")
            return True
        else:
            print(f"❌ Medications failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Medications error: {e}")
        return False

def test_appointments(token):
    """Test appointments API"""
    print("\n📅 Testing Appointments API...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/appointments?page=0&size=20", headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('success'):
            appointments = data.get('data', [])
            print(f"✅ Appointments retrieved - Total: {len(appointments)}")
            if appointments:
                print(f"   Sample appointment: {appointments[0].get('type')} on {appointments[0].get('scheduledDate')}")
            else:
                print("   No appointments found")
            return True
        else:
            print(f"❌ Appointments failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Appointments error: {e}")
        return False

def main():
    print("🔵 Testing Ubuzima Backend APIs")
    print("================================")
    
    # Test login
    token, user_id = test_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        return
    
    # Test all APIs
    results = []
    results.append(test_menstrual_cycles(token))
    results.append(test_medications(token))
    results.append(test_appointments(token))
    
    # Try creating a menstrual cycle
    cycle_id = test_create_menstrual_cycle(token)
    
    # Summary
    print(f"\n🔵 API Testing Complete!")
    print("================================")
    successful_tests = sum(results)
    total_tests = len(results)
    print(f"✅ {successful_tests}/{total_tests} APIs working correctly")
    
    if cycle_id:
        print(f"✅ CRUD operations working - Created cycle ID: {cycle_id}")

if __name__ == "__main__":
    main()

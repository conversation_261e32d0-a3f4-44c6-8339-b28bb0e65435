# PowerShell script to test current working report APIs

$baseUrl = "http://localhost:8080/api/v1"
$adminEmail = "<EMAIL>"
$adminPassword = "admin123"

Write-Host "🔧 Testing Current Report APIs..." -ForegroundColor Cyan

# Step 1: Login
Write-Host "`n1. Logging in..." -ForegroundColor Yellow

$loginBody = @{
    email = $adminEmail
    password = $adminPassword
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    
    if ($loginResponse.success -and $loginResponse.data.accessToken) {
        $token = $loginResponse.data.accessToken
        Write-Host "✅ Login successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ Login failed: $($loginResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Step 2: Test existing report generation
Write-Host "`n2. Testing Report Generation..." -ForegroundColor Yellow

$endDate = Get-Date
$startDate = $endDate.AddDays(-30)

$reportTypes = @("user_activity", "health_records", "appointments")

foreach ($reportType in $reportTypes) {
    Write-Host "`n🧪 Testing $reportType report generation..." -ForegroundColor Cyan
    
    $generateBody = @{
        templateId = $reportType
        startDate = $startDate.ToString("yyyy-MM-dd")
        endDate = $endDate.ToString("yyyy-MM-dd")
    } | ConvertTo-Json
    
    try {
        $generateResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/generate" -Method Post -Body $generateBody -Headers $headers
        
        if ($generateResponse.success) {
            Write-Host "✅ $reportType report generated successfully!" -ForegroundColor Green
            $report = $generateResponse.report
            Write-Host "   Report ID: $($report.id)" -ForegroundColor Gray
            Write-Host "   Status: $($report.status)" -ForegroundColor Gray
            Write-Host "   Generated At: $($report.generatedAt)" -ForegroundColor Gray
            Write-Host "   Period: $($report.period)" -ForegroundColor Gray
        } else {
            Write-Host "❌ Report generation failed: $($generateResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Request error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 3: Test dashboard stats (used by reports)
Write-Host "`n3. Testing Dashboard Stats API..." -ForegroundColor Yellow

try {
    $statsResponse = Invoke-RestMethod -Uri "$baseUrl/admin/dashboard/stats" -Method Get -Headers $headers
    
    if ($statsResponse.success) {
        Write-Host "✅ Dashboard stats fetched successfully!" -ForegroundColor Green
        $stats = $statsResponse.data.stats
        Write-Host "   Total Users: $($stats.totalUsers)" -ForegroundColor Gray
        Write-Host "   Total Health Records: $($stats.totalHealthRecords)" -ForegroundColor Gray
        Write-Host "   Total Appointments: $($stats.totalAppointments)" -ForegroundColor Gray
        Write-Host "   Total Facilities: $($stats.totalFacilities)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Failed to fetch dashboard stats: $($statsResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Dashboard stats error: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Test analytics API (used by reports)
Write-Host "`n4. Testing Analytics API..." -ForegroundColor Yellow

try {
    $analyticsUri = "$baseUrl/admin/analytics?startDate=$($startDate.ToString('yyyy-MM-dd'))&endDate=$($endDate.ToString('yyyy-MM-dd'))"
    $analyticsResponse = Invoke-RestMethod -Uri $analyticsUri -Method Get -Headers $headers
    
    if ($analyticsResponse.success) {
        Write-Host "✅ Analytics data fetched successfully!" -ForegroundColor Green
        $analytics = $analyticsResponse.analytics
        Write-Host "   Total Users: $($analytics.totalUsers)" -ForegroundColor Gray
        Write-Host "   Active Users: $($analytics.activeUsers)" -ForegroundColor Gray
        Write-Host "   New Users This Month: $($analytics.newUsersThisMonth)" -ForegroundColor Gray
        Write-Host "   Users by Role: $($analytics.usersByRole)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Failed to fetch analytics: $($analyticsResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Analytics error: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 5: Test health facilities API (used by reports)
Write-Host "`n5. Testing Health Facilities API..." -ForegroundColor Yellow

try {
    $facilitiesResponse = Invoke-RestMethod -Uri "$baseUrl/admin/health-facilities" -Method Get -Headers $headers
    
    if ($facilitiesResponse.success) {
        Write-Host "✅ Health facilities fetched successfully!" -ForegroundColor Green
        $facilities = $facilitiesResponse.data
        Write-Host "   Total Facilities: $($facilities.Count)" -ForegroundColor Gray
        Write-Host "   First 3 facilities:" -ForegroundColor Gray
        for ($i = 0; $i -lt [Math]::Min(3, $facilities.Count); $i++) {
            Write-Host "   - $($facilities[$i].name)" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Failed to fetch health facilities: $($facilitiesResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Health facilities error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Current Report API Test Completed!" -ForegroundColor Green
Write-Host "`n📊 Summary: All tested APIs are working and providing real data for the report system!" -ForegroundColor Cyan

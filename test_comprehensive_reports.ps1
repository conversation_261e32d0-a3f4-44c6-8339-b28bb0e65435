# PowerShell script to test comprehensive report API integration

$baseUrl = "http://localhost:8080/api/v1"
$adminEmail = "<EMAIL>"
$adminPassword = "admin123"

Write-Host "🔧 Testing Comprehensive Report API Integration..." -ForegroundColor Cyan

# Step 1: Login
Write-Host "`n1. Logging in..." -ForegroundColor Yellow

$loginBody = @{
    email = $adminEmail
    password = $adminPassword
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    
    if ($loginResponse.success -and $loginResponse.data.accessToken) {
        $token = $loginResponse.data.accessToken
        Write-Host "✅ Login successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ Login failed: $($loginResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Step 2: Test Report Templates API
Write-Host "`n2. Testing Report Templates API..." -ForegroundColor Yellow

try {
    $templatesResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/templates" -Method Get -Headers $headers
    
    if ($templatesResponse.success) {
        Write-Host "✅ Report templates fetched successfully!" -ForegroundColor Green
        $templates = $templatesResponse.templates
        Write-Host "   Found $($templates.Count) templates:" -ForegroundColor Gray
        foreach ($template in $templates) {
            Write-Host "   - $($template.name) ($($template.id))" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Failed to fetch templates: $($templatesResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Templates API error: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Test Report Summary APIs
Write-Host "`n3. Testing Report Summary APIs..." -ForegroundColor Yellow

$reportTypes = @("user_activity", "health_records", "appointments")

foreach ($reportType in $reportTypes) {
    Write-Host "`n🧪 Testing $reportType summary..." -ForegroundColor Cyan
    
    try {
        $summaryResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/$reportType/summary" -Method Get -Headers $headers
        
        if ($summaryResponse.success) {
            Write-Host "✅ $reportType summary fetched successfully!" -ForegroundColor Green
            $summary = $summaryResponse.summary
            foreach ($key in $summary.Keys) {
                Write-Host "   $key`: $($summary[$key])" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ Failed to fetch $reportType summary: $($summaryResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $reportType summary error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 4: Test Report Details APIs
Write-Host "`n4. Testing Report Details APIs..." -ForegroundColor Yellow

$endDate = Get-Date
$startDate = $endDate.AddDays(-30)

foreach ($reportType in $reportTypes) {
    Write-Host "`n🧪 Testing $reportType details..." -ForegroundColor Cyan
    
    try {
        $detailsUri = "$baseUrl/admin/reports/$reportType/details?startDate=$($startDate.ToString('yyyy-MM-dd'))&endDate=$($endDate.ToString('yyyy-MM-dd'))"
        $detailsResponse = Invoke-RestMethod -Uri $detailsUri -Method Get -Headers $headers
        
        if ($detailsResponse.success) {
            Write-Host "✅ $reportType details fetched successfully!" -ForegroundColor Green
            $details = $detailsResponse.details
            Write-Host "   Details keys: $($details.Keys -join ', ')" -ForegroundColor Gray
        } else {
            Write-Host "❌ Failed to fetch $reportType details: $($detailsResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $reportType details error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 5: Test Report Insights APIs
Write-Host "`n5. Testing Report Insights APIs..." -ForegroundColor Yellow

foreach ($reportType in $reportTypes) {
    Write-Host "`n🧪 Testing $reportType insights..." -ForegroundColor Cyan
    
    try {
        $insightsResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/insights/$reportType" -Method Get -Headers $headers
        
        if ($insightsResponse.success) {
            Write-Host "✅ $reportType insights fetched successfully!" -ForegroundColor Green
            $insights = $insightsResponse.insights
            Write-Host "   Found $($insights.Count) insights:" -ForegroundColor Gray
            foreach ($insight in $insights) {
                Write-Host "   - $insight" -ForegroundColor Gray
            }
        } else {
            Write-Host "❌ Failed to fetch $reportType insights: $($insightsResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $reportType insights error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Step 6: Test PDF Export API
Write-Host "`n6. Testing PDF Export API..." -ForegroundColor Yellow

$exportBody = @{
    reportType = "user_activity"
    startDate = $startDate.ToString("yyyy-MM-dd")
    endDate = $endDate.ToString("yyyy-MM-dd")
} | ConvertTo-Json

try {
    $exportResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/export/pdf" -Method Post -Body $exportBody -Headers $headers
    
    if ($exportResponse.success) {
        Write-Host "✅ PDF export successful!" -ForegroundColor Green
        Write-Host "   PDF URL: $($exportResponse.pdfUrl)" -ForegroundColor Gray
    } else {
        Write-Host "❌ PDF export failed: $($exportResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ PDF export error: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 7: Test System Overview API
Write-Host "`n7. Testing System Overview API..." -ForegroundColor Yellow

try {
    $overviewResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/system/overview" -Method Get -Headers $headers
    
    if ($overviewResponse.success) {
        Write-Host "✅ System overview fetched successfully!" -ForegroundColor Green
        $overview = $overviewResponse.overview
        foreach ($key in $overview.Keys) {
            Write-Host "   $key`: $($overview[$key])" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ Failed to fetch system overview: $($overviewResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ System overview error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Comprehensive Report API Integration Test Completed!" -ForegroundColor Green

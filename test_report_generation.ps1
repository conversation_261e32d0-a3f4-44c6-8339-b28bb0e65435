# PowerShell script to test report generation API

$baseUrl = "http://localhost:8080/api/v1"
$adminEmail = "<EMAIL>"
$adminPassword = "admin123"

Write-Host "🔧 Testing Report Generation API..." -ForegroundColor Cyan

# Step 1: Login
Write-Host "`n1. Logging in..." -ForegroundColor Yellow

$loginBody = @{
    email = $adminEmail
    password = $adminPassword
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    
    if ($loginResponse.success -and $loginResponse.data.accessToken) {
        $token = $loginResponse.data.accessToken
        Write-Host "✅ Login successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ Login failed: $($loginResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Login error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Test report generation
Write-Host "`n2. Testing report generation..." -ForegroundColor Yellow

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Calculate date range (last 30 days)
$endDate = Get-Date
$startDate = $endDate.AddDays(-30)

$reportTypes = @("user_activity", "health_records", "appointments")

foreach ($reportType in $reportTypes) {
    Write-Host "`n🧪 Testing $reportType report generation..." -ForegroundColor Cyan
    
    $generateBody = @{
        templateId = $reportType
        startDate = $startDate.ToString("yyyy-MM-dd")
        endDate = $endDate.ToString("yyyy-MM-dd")
    } | ConvertTo-Json
    
    try {
        $generateResponse = Invoke-RestMethod -Uri "$baseUrl/admin/reports/generate" -Method Post -Body $generateBody -Headers $headers
        
        if ($generateResponse.success) {
            Write-Host "✅ $reportType report generated successfully!" -ForegroundColor Green
            $report = $generateResponse.report
            Write-Host "   Report ID: $($report.id)" -ForegroundColor Gray
            Write-Host "   Status: $($report.status)" -ForegroundColor Gray
            Write-Host "   Generated At: $($report.generatedAt)" -ForegroundColor Gray
            Write-Host "   Period: $($report.period)" -ForegroundColor Gray
        } else {
            Write-Host "❌ Report generation failed: $($generateResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Request error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "   Response: $($_.Exception.Response)" -ForegroundColor Gray
    }
}

Write-Host "`n🎉 Report generation test completed!" -ForegroundColor Green

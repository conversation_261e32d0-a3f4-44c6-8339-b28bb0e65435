package rw.health.ubuzima.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import rw.health.ubuzima.dto.request.UserCreateRequest;
import rw.health.ubuzima.dto.response.UserResponse;
import rw.health.ubuzima.entity.User;
import rw.health.ubuzima.enums.UserRole;
import rw.health.ubuzima.exception.AuthenticationException;
import rw.health.ubuzima.exception.ResourceNotFoundException;
import rw.health.ubuzima.repository.UserRepository;
import rw.health.ubuzima.util.JwtUtil;

import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Transactional
public class AuthService {

    private final UserRepository userRepository;

    private final UserService userService;
    private final JwtUtil jwtUtil;

    public Map<String, Object> register(UserCreateRequest request) {
        // Enforce role-based registration rules
        String email = request.getEmail().toLowerCase();

        // Determine role based on email domain
        UserRole actualRole;
        if (email.endsWith(".rw")) {
            actualRole = UserRole.ADMIN;
        } else {
            actualRole = UserRole.CLIENT;
        }

        // Override the role in the request to prevent clients from making themselves admin
        // and prevent health worker self-registration
        request.setRole(actualRole);

        UserResponse user = userService.createUser(request);
        
        String accessToken = jwtUtil.generateToken(
            user.getEmail(), 
            user.getRole().toString(), 
            user.getId()
        );
        
        String refreshToken = jwtUtil.generateRefreshToken(
            user.getEmail(), 
            user.getRole().toString(), 
            user.getId()
        );

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "User registered successfully");
        response.put("user", user);
        response.put("accessToken", accessToken);
        response.put("refreshToken", refreshToken);
        
        return response;
    }

    public Map<String, Object> login(String email, String password) {
        System.out.println("=== LOGIN DEBUG ===");
        System.out.println("Step 1: Validating password...");

        // Validate credentials
        if (!userService.validatePassword(email, password)) {
            System.out.println("Password validation failed!");
            throw new AuthenticationException("Invalid email or password");
        }
        System.out.println("Step 2: Password validation passed");

        System.out.println("Step 3: Getting user by email...");
        UserResponse user = userService.getUserByEmail(email);
        System.out.println("Step 4: User found: " + user.getName());

        System.out.println("Step 5: Updating last login...");
        // Update last login
        userService.updateLastLogin(email);
        System.out.println("Step 6: Last login updated");

        System.out.println("Step 7: Generating tokens...");
        String accessToken = jwtUtil.generateToken(
            user.getEmail(),
            user.getRole().toString(),
            user.getId()
        );

        String refreshToken = jwtUtil.generateRefreshToken(
            user.getEmail(),
            user.getRole().toString(),
            user.getId()
        );
        System.out.println("Step 8: Tokens generated");

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Login successful");
        response.put("user", user);
        response.put("accessToken", accessToken);
        response.put("refreshToken", refreshToken);

        System.out.println("Step 9: Login completed successfully!");
        return response;
    }

    public Map<String, Object> refreshToken(String refreshToken) {
        try {
            String email = jwtUtil.extractUsername(refreshToken);
            
            if (!jwtUtil.isRefreshToken(refreshToken)) {
                throw new AuthenticationException("Invalid refresh token");
            }
            
            if (!jwtUtil.validateToken(refreshToken, email)) {
                throw new AuthenticationException("Refresh token expired or invalid");
            }

            UserResponse user = userService.getUserByEmail(email);
            
            String newAccessToken = jwtUtil.generateToken(
                user.getEmail(), 
                user.getRole().toString(), 
                user.getId()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Token refreshed successfully");
            response.put("accessToken", newAccessToken);
            
            return response;
            
        } catch (Exception e) {
            throw new AuthenticationException("Invalid refresh token: " + e.getMessage());
        }
    }

    @Transactional(readOnly = true)
    public UserResponse getCurrentUser(String token) {
        try {
            String email = jwtUtil.extractUsername(token);
            return userService.getUserByEmail(email);
        } catch (Exception e) {
            throw new AuthenticationException("Invalid token: " + e.getMessage());
        }
    }

    public boolean validateToken(String token) {
        try {
            String email = jwtUtil.extractUsername(token);
            return jwtUtil.validateToken(token, email);
        } catch (Exception e) {
            return false;
        }
    }



    public void sendPasswordResetEmail(String email) {
        try {
            User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));

            // Generate password reset token
            String resetToken = jwtUtil.generatePasswordResetToken(email);

            // In a real application, you would send an email here
            // For now, we'll just log the token (in production, never log sensitive data)
            System.out.println("Password reset token for " + email + ": " + resetToken);

            // You could save the token to database with expiration time
            // and send email with reset link containing the token

        } catch (Exception e) {
            throw new RuntimeException("Failed to send password reset email: " + e.getMessage());
        }
    }

    public void resetPassword(String resetToken, String newPassword) {
        try {
            if (!jwtUtil.validatePasswordResetToken(resetToken)) {
                throw new AuthenticationException("Invalid or expired reset token");
            }

            String email = jwtUtil.extractUsername(resetToken);
            User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

            // Hash the new password
            String hashedPassword = userService.hashPassword(newPassword);
            user.setPasswordHash(hashedPassword);
            userRepository.save(user);

        } catch (Exception e) {
            throw new RuntimeException("Failed to reset password: " + e.getMessage());
        }
    }

    public void verifyEmail(String verificationToken) {
        try {
            if (!jwtUtil.validateEmailVerificationToken(verificationToken)) {
                throw new AuthenticationException("Invalid or expired verification token");
            }

            String email = jwtUtil.extractUsername(verificationToken);
            User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

            user.setEmailVerified(true);
            userRepository.save(user);

        } catch (Exception e) {
            throw new RuntimeException("Failed to verify email: " + e.getMessage());
        }
    }

    public void resendVerificationEmail(String email) {
        try {
            User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));

            if (user.getEmailVerified()) {
                throw new RuntimeException("Email is already verified");
            }

            // Generate email verification token
            String verificationToken = jwtUtil.generateEmailVerificationToken(email);

            // In a real application, you would send an email here
            System.out.println("Email verification token for " + email + ": " + verificationToken);

        } catch (Exception e) {
            throw new RuntimeException("Failed to resend verification email: " + e.getMessage());
        }
    }
}

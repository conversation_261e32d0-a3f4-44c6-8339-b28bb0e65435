package rw.health.ubuzima.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import rw.health.ubuzima.entity.FileUpload;

import java.util.List;

@Repository
public interface FileUploadRepository extends JpaRepository<FileUpload, String> {
    List<FileUpload> findByUserIdOrderByUploadedAtDesc(String userId);
    List<FileUpload> findByFileTypeOrderByUploadedAtDesc(String fileType);
    List<FileUpload> findByUserIdAndFileTypeOrderByUploadedAtDesc(String userId, String fileType);

    // Education-specific queries
    List<FileUpload> findByLessonIdOrderByUploadedAtDesc(Long lessonId);
    List<FileUpload> findByCategoryOrderByUploadedAtDesc(String category);
    List<FileUpload> findByLessonIdAndFileTypeOrderByUploadedAtDesc(Long lessonId, String fileType);
}

# API Testing Script for Ubuzima Backend
$baseUrl = "http://localhost:8080/api/v1"
$token = ""
$userId = ""

Write-Host "🔵 Testing Ubuzima Backend APIs" -ForegroundColor Blue
Write-Host "================================" -ForegroundColor Blue

# Test 1: Login
Write-Host "`n1. Testing Login API..." -ForegroundColor Yellow
try {
    $loginBody = @{
        email = "<EMAIL>"
        password = "client123"
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/auth/login" -Method POST -ContentType "application/json" -Body $loginBody

    if ($loginResponse.success) {
        $token = $loginResponse.data.accessToken
        Write-Host "✅ Login successful - Token obtained" -ForegroundColor Green
        Write-Host "   Token: $($token.Substring(0, 20))..." -ForegroundColor Cyan
    } else {
        Write-Host "❌ Login failed: $($loginResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Login error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get User Profile
Write-Host "`n2. Testing User Profile API..." -ForegroundColor Yellow
if ($token) {
    try {
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        $profileResponse = Invoke-RestMethod -Uri "$baseUrl/users/profile" -Method GET -Headers $headers
        
        if ($profileResponse.success) {
            $userId = $profileResponse.data.id
            Write-Host "✅ Profile retrieved - User ID: $userId, Name: $($profileResponse.data.name)" -ForegroundColor Green
        } else {
            Write-Host "❌ Profile failed: $($profileResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Profile error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 3: Get Health Records
Write-Host "`n3. Testing Health Records API..." -ForegroundColor Yellow
if ($token -and $userId) {
    try {
        $healthResponse = Invoke-RestMethod -Uri "$baseUrl/user-centric-health/record/$userId" -Method GET -Headers $headers
        
        if ($healthResponse.success) {
            Write-Host "✅ Health records retrieved successfully" -ForegroundColor Green
            Write-Host "   BMI: $($healthResponse.data.bmi), Heart Rate: $($healthResponse.data.heartRateValue)" -ForegroundColor Cyan
        } else {
            Write-Host "❌ Health records failed" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Health records error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 4: Get Menstrual Cycles
Write-Host "`n4. Testing Menstrual Cycles API..." -ForegroundColor Yellow
if ($token) {
    try {
        $menstrualResponse = Invoke-RestMethod -Uri "$baseUrl/menstrual-cycles?page=0&size=20" -Method GET -Headers $headers

        if ($menstrualResponse.success) {
            Write-Host "✅ Menstrual cycles retrieved - Total: $($menstrualResponse.total)" -ForegroundColor Green
            if ($menstrualResponse.cycles.Count -gt 0) {
                Write-Host "   Sample cycle: Start Date: $($menstrualResponse.cycles[0].startDate)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "❌ Menstrual cycles failed: $($menstrualResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Menstrual cycles error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 5: Get Medications
Write-Host "`n5. Testing Medications API..." -ForegroundColor Yellow
if ($token) {
    try {
        $medicationsResponse = Invoke-RestMethod -Uri "$baseUrl/medications?page=0&size=20" -Method GET -Headers $headers

        if ($medicationsResponse.success) {
            Write-Host "✅ Medications retrieved - Total: $($medicationsResponse.total)" -ForegroundColor Green
            if ($medicationsResponse.medications.Count -gt 0) {
                Write-Host "   Sample medication: $($medicationsResponse.medications[0].name)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "❌ Medications failed: $($medicationsResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Medications error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 6: Get Contraception Methods
Write-Host "`n6. Testing Contraception API..." -ForegroundColor Yellow
if ($token) {
    try {
        $contraceptionResponse = Invoke-RestMethod -Uri "$baseUrl/contraception" -Method GET -Headers $headers

        if ($contraceptionResponse.success) {
            Write-Host "✅ Contraception methods retrieved - Total: $($contraceptionResponse.total)" -ForegroundColor Green
            if ($contraceptionResponse.contraceptionMethods.Count -gt 0) {
                Write-Host "   Sample method: $($contraceptionResponse.contraceptionMethods[0].type)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "❌ Contraception failed: $($contraceptionResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Contraception error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 7: Get Appointments
Write-Host "`n7. Testing Appointments API..." -ForegroundColor Yellow
if ($token) {
    try {
        $appointmentsResponse = Invoke-RestMethod -Uri "$baseUrl/appointments?page=0&size=20" -Method GET -Headers $headers

        if ($appointmentsResponse.success) {
            Write-Host "✅ Appointments retrieved - Total: $($appointmentsResponse.total)" -ForegroundColor Green
            if ($appointmentsResponse.appointments.Count -gt 0) {
                Write-Host "   Sample appointment: $($appointmentsResponse.appointments[0].type) on $($appointmentsResponse.appointments[0].scheduledDate)" -ForegroundColor Cyan
            }
        } else {
            Write-Host "❌ Appointments failed: $($appointmentsResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Appointments error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 8: Create Sample Data (if needed)
Write-Host "`n8. Testing Create Operations..." -ForegroundColor Yellow
if ($token -and $userId) {
    # Test creating a menstrual cycle
    try {
        $cycleBody = @{
            startDate = "2025-01-01"
            cycleLength = 28
            flowDuration = 5
            notes = "Test cycle from API"
        } | ConvertTo-Json

        $createCycleResponse = Invoke-RestMethod -Uri "$baseUrl/menstrual-cycles" -Method POST -Headers $headers -Body $cycleBody

        if ($createCycleResponse.success) {
            Write-Host "✅ Menstrual cycle created successfully" -ForegroundColor Green
        } else {
            Write-Host "❌ Create cycle failed: $($createCycleResponse.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Create cycle error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🔵 API Testing Complete!" -ForegroundColor Blue
Write-Host "================================" -ForegroundColor Blue

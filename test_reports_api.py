#!/usr/bin/env python3
"""
Test script to verify all Reports API endpoints are working correctly
"""

import requests
import json
from datetime import datetime, timedelta

BASE_URL = "http://localhost:8080/api/v1"

def test_endpoint(endpoint, method="GET", data=None, params=None):
    """Test a single endpoint"""
    try:
        url = f"{BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, params=params, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=10)
        
        print(f"  {method} {endpoint}")
        print(f"    Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                json_data = response.json()
                print(f"    Success: {json_data.get('success', 'N/A')}")
                if 'stats' in json_data:
                    stats = json_data['stats']
                    print(f"    Data: {len(stats)} stats fields")
                elif 'analytics' in json_data:
                    analytics = json_data['analytics']
                    print(f"    Data: {len(analytics)} analytics fields")
                elif 'data' in json_data:
                    data = json_data['data']
                    if isinstance(data, list):
                        print(f"    Data: {len(data)} items")
                    else:
                        print(f"    Data: {type(data).__name__}")
                return True
            except json.JSONDecodeError:
                print(f"    Error: Invalid JSON response")
                return False
        else:
            print(f"    Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"    Error: {e}")
        return False

def main():
    print("🔍 Testing Reports API Endpoints")
    print("=" * 50)
    
    # Test 1: Dashboard Stats
    print("\n1. Testing Dashboard Stats...")
    dashboard_ok = test_endpoint("/admin/dashboard/stats")
    
    # Test 2: Analytics
    print("\n2. Testing Analytics...")
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    analytics_ok = test_endpoint("/admin/analytics", params={
        "startDate": start_date,
        "endDate": end_date
    })
    
    # Test 3: Health Facilities
    print("\n3. Testing Health Facilities...")
    facilities_ok = test_endpoint("/admin/health-facilities")
    
    # Test 4: Report Templates (might fail if backend not restarted)
    print("\n4. Testing Report Templates...")
    templates_ok = test_endpoint("/admin/reports/templates")
    
    # Test 5: Generated Reports (might fail if backend not restarted)
    print("\n5. Testing Generated Reports...")
    reports_ok = test_endpoint("/admin/reports", params={
        "type": "user_activity",
        "startDate": start_date,
        "endDate": end_date
    })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Dashboard Stats: {'✅' if dashboard_ok else '❌'}")
    print(f"  Analytics: {'✅' if analytics_ok else '❌'}")
    print(f"  Health Facilities: {'✅' if facilities_ok else '❌'}")
    print(f"  Report Templates: {'✅' if templates_ok else '❌'}")
    print(f"  Generated Reports: {'✅' if reports_ok else '❌'}")
    
    core_apis_working = dashboard_ok and analytics_ok and facilities_ok
    
    print(f"\n🎯 Core Report APIs: {'✅ WORKING' if core_apis_working else '❌ ISSUES'}")
    
    if core_apis_working:
        print("\n✅ All core report APIs are working!")
        print("   The reports page should be fully functional with real data.")
    else:
        print("\n❌ Some core APIs have issues.")
        print("   Check backend server status and database connections.")

if __name__ == "__main__":
    main()

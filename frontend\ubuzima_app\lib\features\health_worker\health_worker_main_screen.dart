import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_colors.dart';
import '../../core/providers/auth_provider.dart';
import '../../core/services/api_service.dart';
import '../../core/widgets/loading_overlay.dart';

/// Clean, unified Health Worker Main Screen
/// Uses only working backend APIs with real data integration
class HealthWorkerMainScreen extends ConsumerStatefulWidget {
  const HealthWorkerMainScreen({super.key});

  @override
  ConsumerState<HealthWorkerMainScreen> createState() =>
      _HealthWorkerMainScreenState();
}

class _HealthWorkerMainScreenState
    extends ConsumerState<HealthWorkerMainScreen> {
  int _selectedIndex = 0;
  bool _isLoading = false;

  // Data from working APIs
  Map<String, dynamic> _dashboardStats = {};
  List<Map<String, dynamic>> _assignedClients = [];
  List<Map<String, dynamic>> _appointments = [];
  List<Map<String, dynamic>> _conversations = [];
  int _unreadMessagesCount = 0;

  @override
  void initState() {
    super.initState();
    _loadHealthWorkerData();
  }

  /// Load data using only working backend APIs
  Future<void> _loadHealthWorkerData() async {
    final user = ref.read(currentUserProvider);
    if (user?.id == null) return;

    setState(() => _isLoading = true);

    try {
      await Future.wait([
        _loadDashboardStats(user!.id!),
        _loadAssignedClients(user.id!),
        _loadAppointments(user.id!),
        _loadConversations(user.id!),
        _loadUnreadMessagesCount(user.id!),
      ]);
    } catch (e) {
      debugPrint('Error loading health worker data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Load dashboard statistics
  Future<void> _loadDashboardStats(int healthWorkerId) async {
    try {
      final response = await ApiService.instance.getHealthWorkerDashboardStats(
        healthWorkerId,
      );
      if (response.success && response.data != null) {
        setState(() {
          _dashboardStats = Map<String, dynamic>.from(response.data as Map);
        });
      }
    } catch (e) {
      debugPrint('Error loading dashboard stats: $e');
    }
  }

  /// Load assigned clients
  Future<void> _loadAssignedClients(int healthWorkerId) async {
    try {
      final response = await ApiService.instance.getHealthWorkerClients(
        healthWorkerId,
      );
      if (response.success && response.data != null) {
        final responseData = Map<String, dynamic>.from(response.data as Map);
        setState(() {
          _assignedClients = List<Map<String, dynamic>>.from(
            responseData['clients'] ?? [],
          );
        });
      }
    } catch (e) {
      debugPrint('Error loading assigned clients: $e');
    }
  }

  /// Load appointments
  Future<void> _loadAppointments(int healthWorkerId) async {
    try {
      final response = await ApiService.instance.getHealthWorkerAppointments(
        healthWorkerId,
      );
      if (response.success && response.data != null) {
        final responseData = Map<String, dynamic>.from(response.data as Map);
        setState(() {
          _appointments = List<Map<String, dynamic>>.from(
            responseData['appointments'] ?? [],
          );
        });
      }
    } catch (e) {
      debugPrint('Error loading appointments: $e');
    }
  }

  /// Load conversations using the working direct API approach
  Future<void> _loadConversations(int userId) async {
    try {
      final dio = ApiService.instance.dio;
      final response = await dio.get(
        '/messages/conversations',
        queryParameters: {'userId': userId},
      );

      if (response.statusCode == 200 && response.data != null) {
        Map<String, dynamic> responseData;

        if (response.data is String) {
          responseData = json.decode(response.data);
        } else if (response.data is Map<String, dynamic>) {
          responseData = response.data;
        } else {
          responseData = {};
        }

        if (responseData['success'] == true &&
            responseData.containsKey('conversations')) {
          List<dynamic> partners = responseData['conversations'] ?? [];
          setState(() {
            _conversations =
                partners
                    .map((partner) => Map<String, dynamic>.from(partner))
                    .toList();
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading conversations: $e');
    }
  }

  /// Load unread messages count
  Future<void> _loadUnreadMessagesCount(int userId) async {
    try {
      final response = await ApiService.instance.getUnreadMessagesCount(userId);
      if (response.success && response.data != null) {
        final responseData = Map<String, dynamic>.from(response.data as Map);
        setState(() {
          _unreadMessagesCount = responseData['unreadCount'] ?? 0;
        });
      }
    } catch (e) {
      debugPrint('Error loading unread messages count: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: IndexedStack(
          index: _selectedIndex,
          children: [
            _buildDashboardTab(),
            _buildClientsTab(),
            _buildAppointmentsTab(),
            _buildMessagesTab(),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: AppColors.primary,
        unselectedItemColor: Colors.grey,
        items: [
          const BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Badge(
              label: Text('${_assignedClients.length}'),
              child: const Icon(Icons.people),
            ),
            label: 'Clients',
          ),
          BottomNavigationBarItem(
            icon: Badge(
              label: Text('${_appointments.length}'),
              child: const Icon(Icons.calendar_today),
            ),
            label: 'Appointments',
          ),
          BottomNavigationBarItem(
            icon: Badge(
              label: Text('$_unreadMessagesCount'),
              child: const Icon(Icons.message),
            ),
            label: 'Messages',
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardTab() {
    final user = ref.watch(currentUserProvider);
    final stats = Map<String, dynamic>.from(_dashboardStats['stats'] ?? {});

    return SafeArea(
      child: RefreshIndicator(
        onRefresh: _loadHealthWorkerData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildWelcomeHeader(user),
              const SizedBox(height: 24),
              _buildStatsCards(stats),
              const SizedBox(height: 24),
              _buildQuickActions(),
              const SizedBox(height: 24),
              _buildRecentActivity(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Text(
              user?.firstName?.substring(0, 1).toUpperCase() ?? 'H',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Welcome back,',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
                Text(
                  user?.firstName ?? 'Health Worker',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Health Worker',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCards(Map<String, dynamic> stats) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildStatCard(
          'Total Clients',
          '${stats['totalClients'] ?? 0}',
          Icons.people,
          AppColors.primary,
        ),
        _buildStatCard(
          'Total Appointments',
          '${stats['totalAppointments'] ?? 0}',
          Icons.calendar_today,
          AppColors.success,
        ),
        _buildStatCard(
          'Today\'s Appointments',
          '${stats['todayAppointments'] ?? 0}',
          Icons.today,
          AppColors.warning,
        ),
        _buildStatCard(
          'Completed',
          '${stats['completedAppointments'] ?? 0}',
          Icons.check_circle,
          AppColors.info,
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 24),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          Text(title, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        // Quick actions will be implemented in the next iteration
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text('Quick actions coming soon...'),
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activity',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        // Recent activity will be implemented in the next iteration
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text('Recent activity coming soon...'),
        ),
      ],
    );
  }

  Widget _buildClientsTab() {
    return SafeArea(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              children: [
                const Text(
                  'My Clients',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  '${_assignedClients.length} clients',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Expanded(
            child:
                _assignedClients.isEmpty
                    ? _buildEmptyState(
                      'No Clients Assigned',
                      'Assigned clients will appear here',
                      Icons.people_outline,
                    )
                    : RefreshIndicator(
                      onRefresh: _loadHealthWorkerData,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _assignedClients.length,
                        itemBuilder: (context, index) {
                          final client = _assignedClients[index];
                          return _buildClientCard(client);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsTab() {
    return SafeArea(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              children: [
                const Text(
                  'Appointments',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  '${_appointments.length} appointments',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Expanded(
            child:
                _appointments.isEmpty
                    ? _buildEmptyState(
                      'No Appointments',
                      'Scheduled appointments will appear here',
                      Icons.calendar_today_outlined,
                    )
                    : RefreshIndicator(
                      onRefresh: _loadHealthWorkerData,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _appointments.length,
                        itemBuilder: (context, index) {
                          final appointment = _appointments[index];
                          return _buildAppointmentCard(appointment);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesTab() {
    return SafeArea(
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Row(
              children: [
                const Text(
                  'Messages',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Text(
                  '${_conversations.length} conversations',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Expanded(
            child:
                _conversations.isEmpty
                    ? _buildEmptyState(
                      'No Messages Yet',
                      'Start conversations with your clients',
                      Icons.message_outlined,
                    )
                    : RefreshIndicator(
                      onRefresh: _loadHealthWorkerData,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _conversations.length,
                        itemBuilder: (context, index) {
                          final conversation = _conversations[index];
                          return _buildConversationCard(conversation);
                        },
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Colors.grey.withValues(alpha: 0.6)),
          const SizedBox(height: 16),
          Text(
            title,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildClientCard(Map<String, dynamic> client) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Text(
            (client['name'] ?? 'U').substring(0, 1).toUpperCase(),
            style: TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          client['name'] ?? 'Unknown Client',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [Text(client['email'] ?? ''), Text(client['phone'] ?? '')],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _viewClientDetails(client),
      ),
    );
  }

  Widget _buildAppointmentCard(Map<String, dynamic> appointment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.success.withValues(alpha: 0.1),
          child: Icon(Icons.calendar_today, color: AppColors.success),
        ),
        title: Text(
          appointment['user']?['name'] ?? 'Unknown Client',
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(appointment['appointmentDate'] ?? ''),
            Text(appointment['status'] ?? ''),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _viewAppointmentDetails(appointment),
      ),
    );
  }

  Widget _buildConversationCard(Map<String, dynamic> conversation) {
    final name = conversation['name'] ?? 'Unknown User';
    final lastMessage = conversation['lastMessage'] ?? '';
    final unreadCount = conversation['unreadCount'] ?? 0;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.info.withValues(alpha: 0.1),
          child: Text(
            name.substring(0, 1).toUpperCase(),
            style: TextStyle(
              color: AppColors.info,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(name, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(
          lastMessage.isEmpty ? 'No messages yet' : lastMessage,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        trailing:
            unreadCount > 0
                ? Container(
                  padding: const EdgeInsets.all(6),
                  decoration: const BoxDecoration(
                    color: AppColors.error,
                    shape: BoxShape.circle,
                  ),
                  child: Text(
                    '$unreadCount',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
                : const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () => _viewConversation(conversation),
      ),
    );
  }

  void _viewClientDetails(Map<String, dynamic> client) {
    // TODO: Navigate to client details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('View details for ${client['name']}')),
    );
  }

  void _viewAppointmentDetails(Map<String, dynamic> appointment) {
    // TODO: Navigate to appointment details screen
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('View appointment details')));
  }

  void _viewConversation(Map<String, dynamic> conversation) {
    // TODO: Navigate to conversation screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Open conversation with ${conversation['name']}')),
    );
  }
}

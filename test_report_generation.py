#!/usr/bin/env python3
"""
Test script to verify report generation API functionality
"""

import requests
import json
import sys
from datetime import datetime, timedelta

# API Configuration
BASE_URL = "http://localhost:8080/api/v1"
ADMIN_EMAIL = "<EMAIL>"
ADMIN_PASSWORD = "admin123"

def login():
    """Login and get auth token"""
    login_url = f"{BASE_URL}/auth/login"
    login_data = {
        "email": ADMIN_EMAIL,
        "password": ADMIN_PASSWORD
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data', {}).get('accessToken'):
                return data['data']['accessToken']
        print(f"Login failed: {response.text}")
        return None
    except Exception as e:
        print(f"Login error: {e}")
        return None

def test_report_generation(token):
    """Test report generation endpoint"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Calculate date range (last 30 days)
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    report_types = ["user_activity", "health_records", "appointments"]
    
    for report_type in report_types:
        print(f"\n🧪 Testing {report_type} report generation...")
        
        generate_url = f"{BASE_URL}/admin/reports/generate"
        generate_data = {
            "templateId": report_type,
            "startDate": start_date.strftime("%Y-%m-%d"),
            "endDate": end_date.strftime("%Y-%m-%d")
        }
        
        try:
            response = requests.post(generate_url, json=generate_data, headers=headers)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ {report_type} report generated successfully!")
                    report = data.get('report', {})
                    print(f"   Report ID: {report.get('id')}")
                    print(f"   Status: {report.get('status')}")
                    print(f"   Generated At: {report.get('generatedAt')}")
                    print(f"   Period: {report.get('period')}")
                else:
                    print(f"❌ Report generation failed: {data.get('message')}")
            else:
                print(f"❌ HTTP Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Request error: {e}")

def main():
    print("🔧 Testing Report Generation API...")
    
    # Step 1: Login
    print("\n1. Logging in...")
    token = login()
    if not token:
        print("❌ Failed to login. Exiting.")
        sys.exit(1)
    
    print("✅ Login successful!")
    
    # Step 2: Test report generation
    print("\n2. Testing report generation...")
    test_report_generation(token)
    
    print("\n🎉 Report generation test completed!")

if __name__ == "__main__":
    main()

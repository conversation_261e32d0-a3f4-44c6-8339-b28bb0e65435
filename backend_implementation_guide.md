# 🏥 Ubuzima Health Data Implementation Guide

## 📊 Database Schema

### 1. Health Records Table
```sql
CREATE TABLE health_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type ENUM('heart_rate', 'weight', 'blood_pressure', 'temperature') NOT NULL,
    value DECIMAL(10,2), -- For single values (heart rate, weight, temperature)
    systolic INT, -- For blood pressure
    diastolic INT, -- For blood pressure
    unit VARCHAR(10) NOT NULL, -- 'bpm', 'kg', 'mmHg', '°C'
    notes TEXT,
    recorded_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_type_date (user_id, type, recorded_at),
    INDEX idx_recorded_at (recorded_at)
);
```

### 2. Health Statistics Table (for aggregated data)
```sql
CREATE TABLE health_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    metric_type ENUM('heart_rate', 'weight', 'blood_pressure', 'temperature') NOT NULL,
    period ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    avg_value DECIMAL(10,2),
    min_value DECIMAL(10,2),
    max_value DECIMAL(10,2),
    count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_metric_period (user_id, metric_type, period, period_start)
);
```

## 🎯 Backend API Endpoints

### 1. Health Records Controller (Java Spring Boot)

```java
@RestController
@RequestMapping("/api/health-records")
@CrossOrigin(origins = "*")
public class HealthRecordController {

    @Autowired
    private HealthRecordService healthRecordService;

    @PostMapping
    public ResponseEntity<Map<String, Object>> createHealthRecord(
            @RequestBody HealthRecordRequest request,
            Authentication authentication) {
        try {
            Long userId = getUserIdFromAuth(authentication);
            HealthRecord record = healthRecordService.createHealthRecord(userId, request);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("record", record);
            response.put("message", "Health record created successfully");
            
            return ResponseEntity.status(201).body(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(Map.of("success", false, "message", e.getMessage()));
        }
    }

    @GetMapping
    public ResponseEntity<Map<String, Object>> getHealthRecords(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            Authentication authentication) {
        try {
            Long userId = getUserIdFromAuth(authentication);
            Page<HealthRecord> records = healthRecordService.getHealthRecords(
                userId, type, startDate, endDate, PageRequest.of(page, size));
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("records", records.getContent());
            response.put("totalElements", records.getTotalElements());
            response.put("totalPages", records.getTotalPages());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(Map.of("success", false, "message", e.getMessage()));
        }
    }

    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getHealthStatistics(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String period,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Authentication authentication) {
        try {
            Long userId = getUserIdFromAuth(authentication);
            Map<String, Object> statistics = healthRecordService.getHealthStatistics(
                userId, type, period, startDate, endDate);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("statistics", statistics);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(Map.of("success", false, "message", e.getMessage()));
        }
    }

    @GetMapping("/latest")
    public ResponseEntity<Map<String, Object>> getLatestRecords(
            Authentication authentication) {
        try {
            Long userId = getUserIdFromAuth(authentication);
            Map<String, HealthRecord> latestRecords = healthRecordService.getLatestRecords(userId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("records", latestRecords);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(Map.of("success", false, "message", e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteHealthRecord(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            Long userId = getUserIdFromAuth(authentication);
            healthRecordService.deleteHealthRecord(id, userId);
            
            return ResponseEntity.ok(Map.of("success", true, "message", "Record deleted successfully"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(Map.of("success", false, "message", e.getMessage()));
        }
    }

    private Long getUserIdFromAuth(Authentication authentication) {
        // Extract user ID from JWT token or authentication object
        return ((UserPrincipal) authentication.getPrincipal()).getId();
    }
}
```

### 2. Health Record Model

```java
@Entity
@Table(name = "health_records")
public class HealthRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private HealthRecordType type;

    @Column(precision = 10, scale = 2)
    private BigDecimal value;

    private Integer systolic;
    private Integer diastolic;

    @Column(length = 10, nullable = false)
    private String unit;

    @Column(columnDefinition = "TEXT")
    private String notes;

    @Column(name = "recorded_at", nullable = false)
    private LocalDateTime recordedAt;

    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors, getters, setters
    public HealthRecord() {}

    // Getters and setters...
}

enum HealthRecordType {
    HEART_RATE("heart_rate"),
    WEIGHT("weight"),
    BLOOD_PRESSURE("blood_pressure"),
    TEMPERATURE("temperature");

    private final String value;

    HealthRecordType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
```

### 3. Health Record Service

```java
@Service
@Transactional
public class HealthRecordService {

    @Autowired
    private HealthRecordRepository healthRecordRepository;

    @Autowired
    private HealthStatisticsService healthStatisticsService;

    public HealthRecord createHealthRecord(Long userId, HealthRecordRequest request) {
        HealthRecord record = new HealthRecord();
        record.setUserId(userId);
        record.setType(HealthRecordType.valueOf(request.getType().toUpperCase()));
        record.setRecordedAt(request.getRecordedAt());
        record.setNotes(request.getNotes());

        switch (record.getType()) {
            case BLOOD_PRESSURE:
                record.setSystolic(request.getSystolic());
                record.setDiastolic(request.getDiastolic());
                record.setUnit("mmHg");
                break;
            case HEART_RATE:
                record.setValue(request.getValue());
                record.setUnit("bpm");
                break;
            case WEIGHT:
                record.setValue(request.getValue());
                record.setUnit("kg");
                break;
            case TEMPERATURE:
                record.setValue(request.getValue());
                record.setUnit("°C");
                break;
        }

        HealthRecord savedRecord = healthRecordRepository.save(record);
        
        // Update statistics asynchronously
        healthStatisticsService.updateStatistics(userId, record.getType(), record.getRecordedAt().toLocalDate());
        
        return savedRecord;
    }

    public Page<HealthRecord> getHealthRecords(Long userId, String type, LocalDate startDate, LocalDate endDate, Pageable pageable) {
        if (type != null && startDate != null && endDate != null) {
            return healthRecordRepository.findByUserIdAndTypeAndRecordedAtBetween(
                userId, HealthRecordType.valueOf(type.toUpperCase()), 
                startDate.atStartOfDay(), endDate.atTime(23, 59, 59), pageable);
        } else if (type != null) {
            return healthRecordRepository.findByUserIdAndType(userId, HealthRecordType.valueOf(type.toUpperCase()), pageable);
        } else if (startDate != null && endDate != null) {
            return healthRecordRepository.findByUserIdAndRecordedAtBetween(
                userId, startDate.atStartOfDay(), endDate.atTime(23, 59, 59), pageable);
        } else {
            return healthRecordRepository.findByUserId(userId, pageable);
        }
    }

    public Map<String, HealthRecord> getLatestRecords(Long userId) {
        Map<String, HealthRecord> latestRecords = new HashMap<>();
        
        for (HealthRecordType type : HealthRecordType.values()) {
            Optional<HealthRecord> latest = healthRecordRepository.findTopByUserIdAndTypeOrderByRecordedAtDesc(userId, type);
            if (latest.isPresent()) {
                latestRecords.put(type.getValue(), latest.get());
            }
        }
        
        return latestRecords;
    }

    public Map<String, Object> getHealthStatistics(Long userId, String type, String period, LocalDate startDate, LocalDate endDate) {
        // Implementation for getting aggregated statistics
        // This would involve complex queries to calculate averages, trends, etc.
        return healthStatisticsService.getStatistics(userId, type, period, startDate, endDate);
    }

    public void deleteHealthRecord(Long recordId, Long userId) {
        HealthRecord record = healthRecordRepository.findByIdAndUserId(recordId, userId)
            .orElseThrow(() -> new RuntimeException("Health record not found"));
        
        healthRecordRepository.delete(record);
        
        // Update statistics after deletion
        healthStatisticsService.updateStatistics(userId, record.getType(), record.getRecordedAt().toLocalDate());
    }
}
```

## 📱 Frontend Integration Points

### 1. Data Input Flow
```
User Input → HealthDataInputScreen → HealthTrackingService → Backend API → Database
```

### 2. Data Display Flow
```
Database → Backend API → HealthTrackingService → HealthTrackingScreen → UI Components
```

### 3. Voice Input Processing
```
Voice Command → Speech Recognition → Data Parsing → Form Population → API Call
```

## 🔄 Real-time Data Sync

### 1. Automatic Refresh
- Refresh data when returning from input screen
- Periodic background sync every 5 minutes
- Pull-to-refresh functionality

### 2. Offline Support
- Store data locally when offline
- Sync when connection restored
- Show offline indicators

### 3. Data Validation
- Frontend validation for immediate feedback
- Backend validation for data integrity
- Consistent error handling

## 📊 Chart Data Processing

### 1. Data Aggregation
- Daily averages for charts
- Weekly/monthly trends
- Comparison with previous periods

### 2. Chart Updates
- Real-time chart updates after new data
- Smooth animations for data changes
- Interactive chart features

This implementation provides a complete health data tracking system with voice input, real-time sync, and comprehensive data visualization! 🚀

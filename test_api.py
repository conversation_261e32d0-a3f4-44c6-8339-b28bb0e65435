#!/usr/bin/env python3
"""
Simple script to test the Ubuzima backend API endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8080"

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health endpoint failed: {e}")
        return False

def test_facilities_endpoint():
    """Test the facilities endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/facilities")
        print(f"Facilities endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Facilities response: {json.dumps(data, indent=2)}")
            if data.get('success') and data.get('facilities'):
                print(f"Found {len(data['facilities'])} facilities")
                for facility in data['facilities']:
                    print(f"  - {facility.get('name')} ({facility.get('facilityType')})")
        return response.status_code == 200
    except Exception as e:
        print(f"Facilities endpoint failed: {e}")
        return False

def test_health_workers_endpoint():
    """Test the health workers endpoint for facility 1"""
    try:
        response = requests.get(f"{BASE_URL}/facilities/1/health-workers")
        print(f"Health workers endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Health workers response: {json.dumps(data, indent=2)}")
            if data.get('success') and data.get('healthWorkers'):
                print(f"Found {len(data['healthWorkers'])} health workers")
                for worker in data['healthWorkers']:
                    print(f"  - {worker.get('name')} ({worker.get('role')})")
        return response.status_code == 200
    except Exception as e:
        print(f"Health workers endpoint failed: {e}")
        return False

def test_available_slots_endpoint():
    """Test the available slots endpoint"""
    try:
        params = {
            'facilityId': '1',
            'date': '2024-01-15'
        }
        response = requests.get(f"{BASE_URL}/appointments/available-slots", params=params)
        print(f"Available slots endpoint: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Available slots response: {json.dumps(data, indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Available slots endpoint failed: {e}")
        return False

def main():
    print("🧪 Testing Ubuzima Backend API Endpoints")
    print("=" * 50)
    
    # Test health endpoint
    print("\n1. Testing Health Endpoint...")
    health_ok = test_health_endpoint()
    
    if not health_ok:
        print("❌ Backend is not running or not accessible")
        return
    
    print("✅ Backend is running")
    
    # Test facilities endpoint
    print("\n2. Testing Facilities Endpoint...")
    facilities_ok = test_facilities_endpoint()
    
    # Test health workers endpoint
    print("\n3. Testing Health Workers Endpoint...")
    workers_ok = test_health_workers_endpoint()
    
    # Test available slots endpoint
    print("\n4. Testing Available Slots Endpoint...")
    slots_ok = test_available_slots_endpoint()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"  Health: {'✅' if health_ok else '❌'}")
    print(f"  Facilities: {'✅' if facilities_ok else '❌'}")
    print(f"  Health Workers: {'✅' if workers_ok else '❌'}")
    print(f"  Available Slots: {'✅' if slots_ok else '❌'}")
    
    if all([health_ok, facilities_ok, workers_ok, slots_ok]):
        print("\n🎉 All API endpoints are working!")
    else:
        print("\n⚠️  Some endpoints need attention")

if __name__ == "__main__":
    main()
